import { Op } from 'sequelize';
import { Invoice, InvoiceCreationAttributes } from '../models/Invoice';
import { User } from '../models/User';

export class InvoiceService {
  public async createInvoice(invoiceData: InvoiceCreationAttributes): Promise<Invoice> {
    // Generate invoice number
    const invoiceNumber = await this.generateInvoiceNumber();
    
    // Calculate totals
    const subtotal = invoiceData.items.reduce((sum, item) => sum + item.amount, 0);
    const total = subtotal + invoiceData.tax;

    const invoice = await Invoice.create({
      ...invoiceData,
      invoiceNumber,
      subtotal,
      total,
    });

    return invoice;
  }

  public async getInvoices(
    userId: number,
    page: number = 1,
    limit: number = 10,
    status?: string
  ): Promise<{ invoices: Invoice[]; total: number; totalPages: number }> {
    const offset = (page - 1) * limit;
    
    const whereClause: any = { userId };
    if (status) {
      whereClause.status = status;
    }

    const { count, rows } = await Invoice.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name', 'email'],
        },
      ],
      order: [['createdAt', 'DESC']],
      limit,
      offset,
    });

    return {
      invoices: rows,
      total: count,
      totalPages: Math.ceil(count / limit),
    };
  }

  public async getInvoiceById(id: number, userId: number): Promise<Invoice | null> {
    return await Invoice.findOne({
      where: { id, userId },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name', 'email'],
        },
      ],
    });
  }

  public async updateInvoice(
    id: number,
    userId: number,
    updateData: Partial<InvoiceCreationAttributes>
  ): Promise<Invoice | null> {
    const invoice = await Invoice.findOne({ where: { id, userId } });
    
    if (!invoice) {
      return null;
    }

    // Recalculate totals if items are updated
    if (updateData.items) {
      const subtotal = updateData.items.reduce((sum, item) => sum + item.amount, 0);
      updateData.subtotal = subtotal;
      updateData.total = subtotal + (updateData.tax || invoice.tax);
    }

    await invoice.update(updateData);
    return invoice;
  }

  public async deleteInvoice(id: number, userId: number): Promise<boolean> {
    const result = await Invoice.destroy({
      where: { id, userId },
    });

    return result > 0;
  }

  public async generateInvoicePDF(id: number, userId: number): Promise<Buffer | null> {
    const invoice = await this.getInvoiceById(id, userId);
    
    if (!invoice) {
      return null;
    }

    // TODO: Implement PDF generation logic
    // You can use libraries like puppeteer, jsPDF, or pdfkit
    // For now, returning a placeholder
    return Buffer.from('PDF content placeholder');
  }

  private async generateInvoiceNumber(): Promise<string> {
    const currentYear = new Date().getFullYear();
    const prefix = `INV-${currentYear}-`;
    
    // Find the last invoice number for the current year
    const lastInvoice = await Invoice.findOne({
      where: {
        invoiceNumber: {
          [Op.like]: `${prefix}%`,
        },
      },
      order: [['createdAt', 'DESC']],
    });

    let nextNumber = 1;
    if (lastInvoice) {
      const lastNumber = parseInt(lastInvoice.invoiceNumber.split('-').pop() || '0');
      nextNumber = lastNumber + 1;
    }

    return `${prefix}${nextNumber.toString().padStart(4, '0')}`;
  }

  public async updateInvoiceStatus(id: number, userId: number, status: 'draft' | 'sent' | 'paid' | 'overdue'): Promise<Invoice | null> {
    const invoice = await Invoice.findOne({ where: { id, userId } });

    if (!invoice) {
      return null;
    }

    await invoice.update({ status });
    return invoice;
  }

  public async getInvoicesByStatus(userId: number, status: string): Promise<Invoice[]> {
    return await Invoice.findAll({
      where: { userId, status },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name', 'email'],
        },
      ],
      order: [['createdAt', 'DESC']],
    });
  }
}
