{"version": 3, "file": "errorHandler.js", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": ";;;AAMO,MAAM,YAAY,GAAG,CAC1B,KAAkB,EAClB,GAAY,EACZ,GAAa,EACb,IAAkB,EACZ,EAAE;IACR,IAAI,UAAU,GAAG,KAAK,CAAC,UAAU,IAAI,GAAG,CAAC;IACzC,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,uBAAuB,CAAC;IAGvD,IAAI,KAAK,CAAC,IAAI,KAAK,0BAA0B,EAAE,CAAC;QAC9C,UAAU,GAAG,GAAG,CAAC;QACjB,OAAO,GAAG,kBAAkB,CAAC;IAC/B,CAAC;IAGD,IAAI,KAAK,CAAC,IAAI,KAAK,gCAAgC,EAAE,CAAC;QACpD,UAAU,GAAG,GAAG,CAAC;QACjB,OAAO,GAAG,yBAAyB,CAAC;IACtC,CAAC;IAGD,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QACvC,UAAU,GAAG,GAAG,CAAC;QACjB,OAAO,GAAG,eAAe,CAAC;IAC5B,CAAC;IAED,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QACvC,UAAU,GAAG,GAAG,CAAC;QACjB,OAAO,GAAG,eAAe,CAAC;IAC5B,CAAC;IAGD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;QAC3C,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;IAED,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;QAC1B,OAAO,EAAE,KAAK;QACd,OAAO;QACP,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC;KACtE,CAAC,CAAC;AACL,CAAC,CAAC;AA1CW,QAAA,YAAY,gBA0CvB"}