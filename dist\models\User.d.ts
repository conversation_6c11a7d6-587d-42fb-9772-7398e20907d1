import { Model, Optional } from 'sequelize';
interface UserAttributes {
    id: number;
    name: string;
    email: string;
    password: string;
    role: 'admin' | 'user';
    isActive: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}
interface UserCreationAttributes extends Optional<UserAttributes, 'id' | 'role' | 'isActive'> {
}
declare class User extends Model<UserAttributes, UserCreationAttributes> implements UserAttributes {
    id: number;
    name: string;
    email: string;
    password: string;
    role: 'admin' | 'user';
    isActive: boolean;
    readonly createdAt: Date;
    readonly updatedAt: Date;
}
export { User, UserAttributes, UserCreationAttributes };
//# sourceMappingURL=User.d.ts.map