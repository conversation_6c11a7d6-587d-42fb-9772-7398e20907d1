import { User, UserCreationAttributes } from '../models/User';
export declare class UserService {
    getUsers(page?: number, limit?: number): Promise<{
        users: User[];
        total: number;
        totalPages: number;
    }>;
    getUserById(id: number): Promise<User | null>;
    updateUser(id: number, updateData: Partial<UserCreationAttributes>): Promise<User | null>;
    deleteUser(id: number): Promise<boolean>;
    toggleUserStatus(id: number): Promise<User | null>;
    getUserByEmail(email: string): Promise<User | null>;
    getActiveUsers(): Promise<User[]>;
    getUserStats(): Promise<{
        total: number;
        active: number;
        inactive: number;
    }>;
}
//# sourceMappingURL=userService.d.ts.map