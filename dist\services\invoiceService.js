"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvoiceService = void 0;
const sequelize_1 = require("sequelize");
const Invoice_1 = require("../models/Invoice");
const User_1 = require("../models/User");
class InvoiceService {
    async createInvoice(invoiceData) {
        const invoiceNumber = await this.generateInvoiceNumber();
        const subtotal = invoiceData.items.reduce((sum, item) => sum + item.amount, 0);
        const total = subtotal + invoiceData.tax;
        const invoice = await Invoice_1.Invoice.create({
            ...invoiceData,
            invoiceNumber,
            subtotal,
            total,
        });
        return invoice;
    }
    async getInvoices(userId, page = 1, limit = 10, status) {
        const offset = (page - 1) * limit;
        const whereClause = { userId };
        if (status) {
            whereClause.status = status;
        }
        const { count, rows } = await Invoice_1.Invoice.findAndCountAll({
            where: whereClause,
            include: [
                {
                    model: User_1.User,
                    as: 'user',
                    attributes: ['id', 'name', 'email'],
                },
            ],
            order: [['createdAt', 'DESC']],
            limit,
            offset,
        });
        return {
            invoices: rows,
            total: count,
            totalPages: Math.ceil(count / limit),
        };
    }
    async getInvoiceById(id, userId) {
        return await Invoice_1.Invoice.findOne({
            where: { id, userId },
            include: [
                {
                    model: User_1.User,
                    as: 'user',
                    attributes: ['id', 'name', 'email'],
                },
            ],
        });
    }
    async updateInvoice(id, userId, updateData) {
        const invoice = await Invoice_1.Invoice.findOne({ where: { id, userId } });
        if (!invoice) {
            return null;
        }
        if (updateData.items) {
            const subtotal = updateData.items.reduce((sum, item) => sum + item.amount, 0);
            updateData.subtotal = subtotal;
            updateData.total = subtotal + (updateData.tax || invoice.tax);
        }
        await invoice.update(updateData);
        return invoice;
    }
    async deleteInvoice(id, userId) {
        const result = await Invoice_1.Invoice.destroy({
            where: { id, userId },
        });
        return result > 0;
    }
    async generateInvoicePDF(id, userId) {
        const invoice = await this.getInvoiceById(id, userId);
        if (!invoice) {
            return null;
        }
        return Buffer.from('PDF content placeholder');
    }
    async generateInvoiceNumber() {
        const currentYear = new Date().getFullYear();
        const prefix = `INV-${currentYear}-`;
        const lastInvoice = await Invoice_1.Invoice.findOne({
            where: {
                invoiceNumber: {
                    [sequelize_1.Op.like]: `${prefix}%`,
                },
            },
            order: [['createdAt', 'DESC']],
        });
        let nextNumber = 1;
        if (lastInvoice) {
            const lastNumber = parseInt(lastInvoice.invoiceNumber.split('-').pop() || '0');
            nextNumber = lastNumber + 1;
        }
        return `${prefix}${nextNumber.toString().padStart(4, '0')}`;
    }
    async updateInvoiceStatus(id, userId, status) {
        const invoice = await Invoice_1.Invoice.findOne({ where: { id, userId } });
        if (!invoice) {
            return null;
        }
        await invoice.update({ status });
        return invoice;
    }
    async getInvoicesByStatus(userId, status) {
        return await Invoice_1.Invoice.findAll({
            where: { userId, status },
            include: [
                {
                    model: User_1.User,
                    as: 'user',
                    attributes: ['id', 'name', 'email'],
                },
            ],
            order: [['createdAt', 'DESC']],
        });
    }
}
exports.InvoiceService = InvoiceService;
//# sourceMappingURL=invoiceService.js.map