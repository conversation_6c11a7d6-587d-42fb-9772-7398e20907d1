import { Request, Response, NextFunction } from 'express';
export declare const validateRegistration: (req: Request, res: Response, next: NextFunction) => void;
export declare const validateLogin: (req: Request, res: Response, next: NextFunction) => void;
export declare const validateInvoice: (req: Request, res: Response, next: NextFunction) => void;
export declare const validateUserUpdate: (req: Request, res: Response, next: NextFunction) => void;
//# sourceMappingURL=validation.d.ts.map