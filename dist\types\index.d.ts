export interface ApiResponse<T = any> {
    success: boolean;
    message: string;
    data?: T;
}
export interface PaginationQuery {
    page?: number;
    limit?: number;
}
export interface PaginatedResponse<T> {
    data: T[];
    total: number;
    totalPages: number;
    currentPage: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
}
export interface JWTPayload {
    id: number;
    email: string;
    role: 'admin' | 'user';
    iat?: number;
    exp?: number;
}
export interface InvoiceItem {
    description: string;
    quantity: number;
    rate: number;
    amount: number;
}
export interface InvoiceFilters {
    status?: 'draft' | 'sent' | 'paid' | 'overdue';
    startDate?: Date;
    endDate?: Date;
    clientName?: string;
}
//# sourceMappingURL=index.d.ts.map