import { Router } from 'express';
import { UserController } from '../controllers/userController';
import { authMiddleware, adminMiddleware } from '../middleware/authMiddleware';
import { validateUserUpdate } from '../middleware/validation';

const router = Router();
const userController = new UserController();

// All routes are protected
router.use(authMiddleware);

/**
 * @route   GET /api/v1/users
 * @desc    Get all users (Admin only)
 * @access  Private/Admin
 * @query   page, limit
 */
router.get('/', adminMiddleware, userController.getUsers);

/**
 * @route   GET /api/v1/users/:id
 * @desc    Get user by ID (Admin only)
 * @access  Private/Admin
 */
router.get('/:id', adminMiddleware, userController.getUserById);

/**
 * @route   PUT /api/v1/users/:id
 * @desc    Update user (Admin only)
 * @access  Private/Admin
 */
router.put('/:id', adminMiddleware, validateUserUpdate, userController.updateUser);

/**
 * @route   DELETE /api/v1/users/:id
 * @desc    Delete user (Admin only)
 * @access  Private/Admin
 */
router.delete('/:id', adminMiddleware, userController.deleteUser);

/**
 * @route   PATCH /api/v1/users/:id/toggle-status
 * @desc    Toggle user active status (Admin only)
 * @access  Private/Admin
 */
router.patch('/:id/toggle-status', adminMiddleware, userController.toggleUserStatus);

/**
 * @route   GET /api/v1/users/stats/overview
 * @desc    Get user statistics (Admin only)
 * @access  Private/Admin
 */
router.get('/stats/overview', adminMiddleware, (req, res) => {
  // This can be implemented later for user statistics
  res.json({ message: 'User statistics endpoint - to be implemented' });
});

export { router as userRoutes };
