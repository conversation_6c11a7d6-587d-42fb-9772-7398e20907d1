"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.userRoutes = void 0;
const express_1 = require("express");
const userController_1 = require("../controllers/userController");
const authMiddleware_1 = require("../middleware/authMiddleware");
const validation_1 = require("../middleware/validation");
const router = (0, express_1.Router)();
exports.userRoutes = router;
const userController = new userController_1.UserController();
router.use(authMiddleware_1.authMiddleware);
router.get('/', authMiddleware_1.adminMiddleware, userController.getUsers);
router.get('/:id', authMiddleware_1.adminMiddleware, userController.getUserById);
router.put('/:id', authMiddleware_1.adminMiddleware, validation_1.validateUserUpdate, userController.updateUser);
router.delete('/:id', authMiddleware_1.adminMiddleware, userController.deleteUser);
router.patch('/:id/toggle-status', authMiddleware_1.adminMiddleware, userController.toggleUserStatus);
router.get('/stats/overview', authMiddleware_1.adminMiddleware, (req, res) => {
    res.json({ message: 'User statistics endpoint - to be implemented' });
});
//# sourceMappingURL=userRoutes.js.map