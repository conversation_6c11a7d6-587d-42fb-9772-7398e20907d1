"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.apiV1Routes = void 0;
const express_1 = require("express");
const authRoutes_1 = require("./authRoutes");
const invoiceRoutes_1 = require("./invoiceRoutes");
const userRoutes_1 = require("./userRoutes");
const router = (0, express_1.Router)();
exports.apiV1Routes = router;
router.use('/auth', authRoutes_1.authRoutes);
router.use('/invoices', invoiceRoutes_1.invoiceRoutes);
router.use('/users', userRoutes_1.userRoutes);
//# sourceMappingURL=index.js.map