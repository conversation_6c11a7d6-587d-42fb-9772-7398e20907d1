"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Invoice = void 0;
const sequelize_1 = require("sequelize");
const database_1 = require("../config/database");
const User_1 = require("./User");
class Invoice extends sequelize_1.Model {
}
exports.Invoice = Invoice;
Invoice.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
    },
    invoiceNumber: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: false,
        unique: true,
    },
    clientName: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: false,
    },
    clientEmail: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: false,
        validate: {
            isEmail: true,
        },
    },
    clientAddress: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: false,
    },
    items: {
        type: sequelize_1.DataTypes.JSONB,
        allowNull: false,
    },
    subtotal: {
        type: sequelize_1.DataTypes.DECIMAL(10, 2),
        allowNull: false,
    },
    tax: {
        type: sequelize_1.DataTypes.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0,
    },
    total: {
        type: sequelize_1.DataTypes.DECIMAL(10, 2),
        allowNull: false,
    },
    status: {
        type: sequelize_1.DataTypes.ENUM('draft', 'sent', 'paid', 'overdue'),
        defaultValue: 'draft',
    },
    dueDate: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
    },
    userId: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        references: {
            model: User_1.User,
            key: 'id',
        },
    },
}, {
    sequelize: database_1.sequelize,
    modelName: 'Invoice',
    tableName: 'invoices',
    timestamps: true,
});
Invoice.belongsTo(User_1.User, { foreignKey: 'userId', as: 'user' });
User_1.User.hasMany(Invoice, { foreignKey: 'userId', as: 'invoices' });
//# sourceMappingURL=Invoice.js.map