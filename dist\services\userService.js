"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
const User_1 = require("../models/User");
const bcryptjs_1 = __importDefault(require("bcryptjs"));
class UserService {
    async getUsers(page = 1, limit = 10) {
        const offset = (page - 1) * limit;
        const { count, rows } = await User_1.User.findAndCountAll({
            attributes: { exclude: ['password'] },
            order: [['createdAt', 'DESC']],
            limit,
            offset,
        });
        return {
            users: rows,
            total: count,
            totalPages: Math.ceil(count / limit),
        };
    }
    async getUserById(id) {
        return await User_1.User.findByPk(id, {
            attributes: { exclude: ['password'] },
        });
    }
    async updateUser(id, updateData) {
        const user = await User_1.User.findByPk(id);
        if (!user) {
            return null;
        }
        if (updateData.password) {
            const saltRounds = 12;
            updateData.password = await bcryptjs_1.default.hash(updateData.password, saltRounds);
        }
        await user.update(updateData);
        return await User_1.User.findByPk(id, {
            attributes: { exclude: ['password'] },
        });
    }
    async deleteUser(id) {
        const result = await User_1.User.destroy({
            where: { id },
        });
        return result > 0;
    }
    async toggleUserStatus(id) {
        const user = await User_1.User.findByPk(id);
        if (!user) {
            return null;
        }
        await user.update({ isActive: !user.isActive });
        return await User_1.User.findByPk(id, {
            attributes: { exclude: ['password'] },
        });
    }
    async getUserByEmail(email) {
        return await User_1.User.findOne({
            where: { email },
            attributes: { exclude: ['password'] },
        });
    }
    async getActiveUsers() {
        return await User_1.User.findAll({
            where: { isActive: true },
            attributes: { exclude: ['password'] },
            order: [['name', 'ASC']],
        });
    }
    async getUserStats() {
        const total = await User_1.User.count();
        const active = await User_1.User.count({ where: { isActive: true } });
        const inactive = total - active;
        return { total, active, inactive };
    }
}
exports.UserService = UserService;
//# sourceMappingURL=userService.js.map