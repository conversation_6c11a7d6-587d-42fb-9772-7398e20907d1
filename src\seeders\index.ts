import { sequelize } from '../config/database';
import { User } from '../models/User';
import { Invoice } from '../models/Invoice';
import bcrypt from 'bcryptjs';

export class DatabaseSeeder {
  public static async seed(): Promise<void> {
    try {
      console.log('Starting database seeding...');

      // Create admin user
      const adminPassword = await bcrypt.hash('admin123', 12);
      const admin = await User.findOrCreate({
        where: { email: '<EMAIL>' },
        defaults: {
          name: 'Admin User',
          email: '<EMAIL>',
          password: adminPassword,
          role: 'admin',
          isActive: true,
        },
      });

      // Create regular user
      const userPassword = await bcrypt.hash('user123', 12);
      const user = await User.findOrCreate({
        where: { email: '<EMAIL>' },
        defaults: {
          name: 'Regular User',
          email: '<EMAIL>',
          password: userPassword,
          role: 'user',
          isActive: true,
        },
      });

      // Create sample invoice
      const sampleInvoice = await Invoice.findOrCreate({
        where: { invoiceNumber: 'INV-2024-0001' },
        defaults: {
          invoiceNumber: 'INV-2024-0001',
          clientName: 'Sample Client',
          clientEmail: '<EMAIL>',
          clientAddress: '123 Main St, City, State 12345',
          items: [
            {
              description: 'Web Development Services',
              quantity: 40,
              rate: 50,
              amount: 2000,
            },
            {
              description: 'Hosting Setup',
              quantity: 1,
              rate: 100,
              amount: 100,
            },
          ],
          subtotal: 2100,
          tax: 210,
          total: 2310,
          status: 'sent',
          dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
          userId: user[0].id,
        },
      });

      console.log('Database seeding completed successfully!');
      console.log('Admin credentials: <EMAIL> / admin123');
      console.log('User credentials: <EMAIL> / user123');
    } catch (error) {
      console.error('Error seeding database:', error);
      throw error;
    }
  }

  public static async run(): Promise<void> {
    try {
      await sequelize.authenticate();
      console.log('Database connected successfully');

      await sequelize.sync({ force: false });
      console.log('Database synchronized');

      await this.seed();
      
      process.exit(0);
    } catch (error) {
      console.error('Seeding failed:', error);
      process.exit(1);
    }
  }
}

// Run seeder if this file is executed directly
if (require.main === module) {
  DatabaseSeeder.run();
}
