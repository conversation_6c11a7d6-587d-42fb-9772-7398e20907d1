"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.models = exports.Invoice = exports.User = void 0;
const User_1 = require("./User");
Object.defineProperty(exports, "User", { enumerable: true, get: function () { return User_1.User; } });
const Invoice_1 = require("./Invoice");
Object.defineProperty(exports, "Invoice", { enumerable: true, get: function () { return Invoice_1.Invoice; } });
exports.models = {
    User: User_1.User,
    Invoice: Invoice_1.Invoice,
};
//# sourceMappingURL=index.js.map