{"name": "invoicegeneratebackend", "version": "1.0.0", "main": "dist/app.js", "scripts": {"build": "tsc", "start": "node dist/app.js", "dev": "ts-node-dev --respawn --transpile-only src/app.ts", "dev:watch": "ts-node-dev --respawn --transpile-only --watch src src/app.ts", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "seed": "ts-node src/seeders/index.ts", "db:sync": "ts-node -e \"import('./src/config/database').then(db => db.sequelize.sync({force: false})).then(() => console.log('Database synced')).catch(console.error)\"", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["invoice", "backend", "express", "typescript", "postgresql", "sequelize"], "author": "", "license": "ISC", "description": "Invoice Generation Backend API with Express, TypeScript, PostgreSQL and Sequelize", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.3", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "pg": "^8.16.3", "pg-hstore": "^2.3.4", "sequelize": "^6.37.7"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.6.2", "rimraf": "^6.0.1", "ts-node-dev": "^2.0.0", "typescript": "^5.9.3"}}