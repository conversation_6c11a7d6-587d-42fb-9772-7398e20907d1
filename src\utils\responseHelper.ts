import { Response } from 'express';
import { ApiResponse, PaginatedResponse } from '../types';

export class ResponseHelper {
  static success<T>(res: Response, message: string, data?: T, statusCode: number = 200): void {
    const response: ApiResponse<T> = {
      success: true,
      message,
      data,
    };
    res.status(statusCode).json(response);
  }

  static error(res: Response, message: string, statusCode: number = 500): void {
    const response: ApiResponse = {
      success: false,
      message,
    };
    res.status(statusCode).json(response);
  }

  static paginated<T>(
    res: Response,
    data: T[],
    total: number,
    page: number,
    limit: number,
    message: string = 'Data retrieved successfully'
  ): void {
    const totalPages = Math.ceil(total / limit);
    const response: PaginatedResponse<T> = {
      data,
      total,
      totalPages,
      currentPage: page,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1,
    };

    res.status(200).json({
      success: true,
      message,
      ...response,
    });
  }
}
