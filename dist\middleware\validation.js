"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateUserUpdate = exports.validateInvoice = exports.validateLogin = exports.validateRegistration = void 0;
const validateRegistration = (req, res, next) => {
    const { name, email, password } = req.body;
    if (!name || !email || !password) {
        res.status(400).json({ message: 'Name, email, and password are required' });
        return;
    }
    if (password.length < 6) {
        res.status(400).json({ message: 'Password must be at least 6 characters long' });
        return;
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        res.status(400).json({ message: 'Please provide a valid email address' });
        return;
    }
    next();
};
exports.validateRegistration = validateRegistration;
const validateLogin = (req, res, next) => {
    const { email, password } = req.body;
    if (!email || !password) {
        res.status(400).json({ message: 'Email and password are required' });
        return;
    }
    next();
};
exports.validateLogin = validateLogin;
const validateInvoice = (req, res, next) => {
    const { clientName, clientEmail, clientAddress, items, dueDate } = req.body;
    if (!clientName || !clientEmail || !clientAddress || !items || !dueDate) {
        res.status(400).json({
            message: 'Client name, email, address, items, and due date are required'
        });
        return;
    }
    if (!Array.isArray(items) || items.length === 0) {
        res.status(400).json({ message: 'At least one item is required' });
        return;
    }
    for (const item of items) {
        if (!item.description || !item.quantity || !item.rate) {
            res.status(400).json({
                message: 'Each item must have description, quantity, and rate'
            });
            return;
        }
        if (item.quantity <= 0 || item.rate <= 0) {
            res.status(400).json({
                message: 'Quantity and rate must be positive numbers'
            });
            return;
        }
        if (!item.amount) {
            item.amount = item.quantity * item.rate;
        }
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(clientEmail)) {
        res.status(400).json({ message: 'Please provide a valid client email address' });
        return;
    }
    next();
};
exports.validateInvoice = validateInvoice;
const validateUserUpdate = (req, res, next) => {
    const { email, password } = req.body;
    if (email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            res.status(400).json({ message: 'Please provide a valid email address' });
            return;
        }
    }
    if (password && password.length < 6) {
        res.status(400).json({ message: 'Password must be at least 6 characters long' });
        return;
    }
    next();
};
exports.validateUserUpdate = validateUserUpdate;
//# sourceMappingURL=validation.js.map