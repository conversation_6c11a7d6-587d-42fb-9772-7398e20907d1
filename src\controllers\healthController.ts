import { Request, Response } from 'express';
import { sequelize } from '../config/database';

export class HealthController {
  public static async healthCheck(req: Request, res: Response): Promise<void> {
    try {
      // Check database connection
      await sequelize.authenticate();
      
      const healthStatus = {
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development',
        version: process.env.npm_package_version || '1.0.0',
        database: 'Connected',
        memory: {
          used: Math.round((process.memoryUsage().heapUsed / 1024 / 1024) * 100) / 100,
          total: Math.round((process.memoryUsage().heapTotal / 1024 / 1024) * 100) / 100,
        },
      };

      res.status(200).json(healthStatus);
    } catch (error) {
      res.status(503).json({
        status: 'ERROR',
        timestamp: new Date().toISOString(),
        error: 'Database connection failed',
      });
    }
  }
}
