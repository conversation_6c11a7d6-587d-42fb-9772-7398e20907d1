import { Model, Optional } from 'sequelize';
interface InvoiceAttributes {
    id: number;
    invoiceNumber: string;
    clientName: string;
    clientEmail: string;
    clientAddress: string;
    items: InvoiceItem[];
    subtotal: number;
    tax: number;
    total: number;
    status: 'draft' | 'sent' | 'paid' | 'overdue';
    dueDate: Date;
    userId: number;
    createdAt?: Date;
    updatedAt?: Date;
}
interface InvoiceItem {
    description: string;
    quantity: number;
    rate: number;
    amount: number;
}
interface InvoiceCreationAttributes extends Optional<InvoiceAttributes, 'id' | 'status'> {
}
declare class Invoice extends Model<InvoiceAttributes, InvoiceCreationAttributes> implements InvoiceAttributes {
    id: number;
    invoiceNumber: string;
    clientName: string;
    clientEmail: string;
    clientAddress: string;
    items: InvoiceItem[];
    subtotal: number;
    tax: number;
    total: number;
    status: 'draft' | 'sent' | 'paid' | 'overdue';
    dueDate: Date;
    userId: number;
    readonly createdAt: Date;
    readonly updatedAt: Date;
}
export { Invoice, InvoiceAttributes, InvoiceCreationAttributes, InvoiceItem };
//# sourceMappingURL=Invoice.d.ts.map