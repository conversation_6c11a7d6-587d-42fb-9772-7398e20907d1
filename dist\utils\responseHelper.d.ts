import { Response } from 'express';
export declare class ResponseHelper {
    static success<T>(res: Response, message: string, data?: T, statusCode?: number): void;
    static error(res: Response, message: string, statusCode?: number): void;
    static paginated<T>(res: Response, data: T[], total: number, page: number, limit: number, message?: string): void;
}
//# sourceMappingURL=responseHelper.d.ts.map