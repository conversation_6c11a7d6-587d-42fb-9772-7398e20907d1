import { User, UserCreationAttributes } from '../models/User';
export declare class AuthService {
    private readonly JWT_SECRET;
    private readonly JWT_EXPIRES_IN;
    createUser(userData: UserCreationAttributes): Promise<User>;
    authenticateUser(email: string, password: string): Promise<{
        token: string;
        user: any;
    } | null>;
    verifyToken(token: string): any;
    getUserById(id: number): Promise<User | null>;
}
//# sourceMappingURL=authService.d.ts.map