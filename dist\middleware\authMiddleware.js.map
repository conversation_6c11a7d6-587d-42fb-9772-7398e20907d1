{"version": 3, "file": "authMiddleware.js", "sourceRoot": "", "sources": ["../../src/middleware/authMiddleware.ts"], "names": [], "mappings": ";;;;;;AACA,gEAA+B;AAC/B,yCAAsC;AAM/B,MAAM,cAAc,GAAG,KAAK,EACjC,GAAyB,EACzB,GAAa,EACb,IAAkB,EACH,EAAE;IACjB,IAAI,CAAC;QACH,MAAM,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QAElE,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,mCAAmC,EAAE,CAAC,CAAC;YACvE,OAAO;QACT,CAAC;QAED,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,iBAAiB,CAAC;QAC/D,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,UAAU,CAAQ,CAAC;QAGrD,MAAM,IAAI,GAAG,MAAM,WAAI,CAAC,OAAO,CAAC;YAC9B,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;YACzC,UAAU,EAAE,EAAE,OAAO,EAAE,CAAC,UAAU,CAAC,EAAE;SACtC,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,4CAA4C,EAAE,CAAC,CAAC;YAChF,OAAO;QACT,CAAC;QAED,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;QAChB,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC,CAAC;IACtD,CAAC;AACH,CAAC,CAAC;AAhCW,QAAA,cAAc,kBAgCzB;AAEK,MAAM,eAAe,GAAG,CAC7B,GAAyB,EACzB,GAAa,EACb,IAAkB,EACZ,EAAE;IACR,IAAI,GAAG,CAAC,IAAI,EAAE,IAAI,KAAK,OAAO,EAAE,CAAC;QAC/B,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,2CAA2C,EAAE,CAAC,CAAC;QAC/E,OAAO;IACT,CAAC;IACD,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAVW,QAAA,eAAe,mBAU1B"}