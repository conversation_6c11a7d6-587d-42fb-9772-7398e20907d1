import { Request, Response } from 'express';
export declare class InvoiceController {
    private invoiceService;
    constructor();
    createInvoice: (req: Request, res: Response) => Promise<void>;
    getInvoices: (req: Request, res: Response) => Promise<void>;
    getInvoiceById: (req: Request, res: Response) => Promise<void>;
    updateInvoice: (req: Request, res: Response) => Promise<void>;
    deleteInvoice: (req: Request, res: Response) => Promise<void>;
    generateInvoicePDF: (req: Request, res: Response) => Promise<void>;
}
//# sourceMappingURL=invoiceController.d.ts.map