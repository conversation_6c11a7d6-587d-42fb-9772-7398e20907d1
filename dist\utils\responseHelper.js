"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseHelper = void 0;
class ResponseHelper {
    static success(res, message, data, statusCode = 200) {
        const response = {
            success: true,
            message,
            data,
        };
        res.status(statusCode).json(response);
    }
    static error(res, message, statusCode = 500) {
        const response = {
            success: false,
            message,
        };
        res.status(statusCode).json(response);
    }
    static paginated(res, data, total, page, limit, message = 'Data retrieved successfully') {
        const totalPages = Math.ceil(total / limit);
        const response = {
            data,
            total,
            totalPages,
            currentPage: page,
            hasNextPage: page < totalPages,
            hasPrevPage: page > 1,
        };
        res.status(200).json({
            success: true,
            message,
            ...response,
        });
    }
}
exports.ResponseHelper = ResponseHelper;
//# sourceMappingURL=responseHelper.js.map