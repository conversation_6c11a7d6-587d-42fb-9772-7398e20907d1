"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserController = void 0;
const userService_1 = require("../services/userService");
class UserController {
    constructor() {
        this.getUsers = async (req, res) => {
            try {
                const { page = 1, limit = 10 } = req.query;
                const users = await this.userService.getUsers(parseInt(page), parseInt(limit));
                res.status(200).json(users);
            }
            catch (error) {
                console.error('Get users error:', error);
                res.status(500).json({ message: 'Internal server error' });
            }
        };
        this.getUserById = async (req, res) => {
            try {
                const { id } = req.params;
                const user = await this.userService.getUserById(parseInt(id));
                if (!user) {
                    res.status(404).json({ message: 'User not found' });
                    return;
                }
                res.status(200).json({ user });
            }
            catch (error) {
                console.error('Get user error:', error);
                res.status(500).json({ message: 'Internal server error' });
            }
        };
        this.updateUser = async (req, res) => {
            try {
                const { id } = req.params;
                const updateData = req.body;
                const user = await this.userService.updateUser(parseInt(id), updateData);
                if (!user) {
                    res.status(404).json({ message: 'User not found' });
                    return;
                }
                res.status(200).json({
                    message: 'User updated successfully',
                    user,
                });
            }
            catch (error) {
                console.error('Update user error:', error);
                res.status(500).json({ message: 'Internal server error' });
            }
        };
        this.deleteUser = async (req, res) => {
            try {
                const { id } = req.params;
                const deleted = await this.userService.deleteUser(parseInt(id));
                if (!deleted) {
                    res.status(404).json({ message: 'User not found' });
                    return;
                }
                res.status(200).json({ message: 'User deleted successfully' });
            }
            catch (error) {
                console.error('Delete user error:', error);
                res.status(500).json({ message: 'Internal server error' });
            }
        };
        this.toggleUserStatus = async (req, res) => {
            try {
                const { id } = req.params;
                const user = await this.userService.toggleUserStatus(parseInt(id));
                if (!user) {
                    res.status(404).json({ message: 'User not found' });
                    return;
                }
                res.status(200).json({
                    message: 'User status updated successfully',
                    user,
                });
            }
            catch (error) {
                console.error('Toggle user status error:', error);
                res.status(500).json({ message: 'Internal server error' });
            }
        };
        this.userService = new userService_1.UserService();
    }
}
exports.UserController = UserController;
//# sourceMappingURL=userController.js.map