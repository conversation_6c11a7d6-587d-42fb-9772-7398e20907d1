"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.authRoutes = void 0;
const express_1 = require("express");
const authController_1 = require("../controllers/authController");
const authMiddleware_1 = require("../middleware/authMiddleware");
const validation_1 = require("../middleware/validation");
const router = (0, express_1.Router)();
exports.authRoutes = router;
const authController = new authController_1.AuthController();
router.post('/register', validation_1.validateRegistration, authController.register);
router.post('/login', validation_1.validateLogin, authController.login);
router.get('/profile', authMiddleware_1.authMiddleware, authController.getProfile);
router.post('/logout', authMiddleware_1.authMiddleware, (req, res) => {
    res.json({
        success: true,
        message: 'Logged out successfully. Please remove token from client.'
    });
});
//# sourceMappingURL=authRoutes.js.map