import { Invoice, InvoiceCreationAttributes } from '../models/Invoice';
export declare class InvoiceService {
    createInvoice(invoiceData: InvoiceCreationAttributes): Promise<Invoice>;
    getInvoices(userId: number, page?: number, limit?: number, status?: string): Promise<{
        invoices: Invoice[];
        total: number;
        totalPages: number;
    }>;
    getInvoiceById(id: number, userId: number): Promise<Invoice | null>;
    updateInvoice(id: number, userId: number, updateData: Partial<InvoiceCreationAttributes>): Promise<Invoice | null>;
    deleteInvoice(id: number, userId: number): Promise<boolean>;
    generateInvoicePDF(id: number, userId: number): Promise<Buffer | null>;
    private generateInvoiceNumber;
    updateInvoiceStatus(id: number, userId: number, status: 'draft' | 'sent' | 'paid' | 'overdue'): Promise<Invoice | null>;
    getInvoicesByStatus(userId: number, status: string): Promise<Invoice[]>;
}
//# sourceMappingURL=invoiceService.d.ts.map