"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const User_1 = require("../models/User");
const authService_1 = require("../services/authService");
class AuthController {
    constructor() {
        this.register = async (req, res) => {
            try {
                const { name, email, password, role } = req.body;
                if (!name || !email || !password) {
                    res.status(400).json({ message: 'Name, email, and password are required' });
                    return;
                }
                const existingUser = await User_1.User.findOne({ where: { email } });
                if (existingUser) {
                    res.status(400).json({ message: 'User already exists with this email' });
                    return;
                }
                const user = await this.authService.createUser({ name, email, password, role });
                res.status(201).json({
                    message: 'User created successfully',
                    user: {
                        id: user.id,
                        name: user.name,
                        email: user.email,
                        role: user.role,
                    },
                });
            }
            catch (error) {
                console.error('Registration error:', error);
                res.status(500).json({ message: 'Internal server error' });
            }
        };
        this.login = async (req, res) => {
            try {
                const { email, password } = req.body;
                if (!email || !password) {
                    res.status(400).json({ message: 'Email and password are required' });
                    return;
                }
                const result = await this.authService.authenticateUser(email, password);
                if (!result) {
                    res.status(401).json({ message: 'Invalid credentials' });
                    return;
                }
                res.status(200).json({
                    message: 'Login successful',
                    token: result.token,
                    user: result.user,
                });
            }
            catch (error) {
                console.error('Login error:', error);
                res.status(500).json({ message: 'Internal server error' });
            }
        };
        this.getProfile = async (req, res) => {
            try {
                const userId = req.user.id;
                const user = await User_1.User.findByPk(userId, {
                    attributes: { exclude: ['password'] },
                });
                if (!user) {
                    res.status(404).json({ message: 'User not found' });
                    return;
                }
                res.status(200).json({ user });
            }
            catch (error) {
                console.error('Get profile error:', error);
                res.status(500).json({ message: 'Internal server error' });
            }
        };
        this.authService = new authService_1.AuthService();
    }
}
exports.AuthController = AuthController;
//# sourceMappingURL=authController.js.map