"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvoiceController = void 0;
const invoiceService_1 = require("../services/invoiceService");
class InvoiceController {
    constructor() {
        this.createInvoice = async (req, res) => {
            try {
                const userId = req.user.id;
                const invoiceData = { ...req.body, userId };
                const invoice = await this.invoiceService.createInvoice(invoiceData);
                res.status(201).json({
                    message: 'Invoice created successfully',
                    invoice,
                });
            }
            catch (error) {
                console.error('Create invoice error:', error);
                res.status(500).json({ message: 'Internal server error' });
            }
        };
        this.getInvoices = async (req, res) => {
            try {
                const userId = req.user.id;
                const { page = 1, limit = 10, status } = req.query;
                const invoices = await this.invoiceService.getInvoices(userId, parseInt(page), parseInt(limit), status);
                res.status(200).json(invoices);
            }
            catch (error) {
                console.error('Get invoices error:', error);
                res.status(500).json({ message: 'Internal server error' });
            }
        };
        this.getInvoiceById = async (req, res) => {
            try {
                const { id } = req.params;
                const userId = req.user.id;
                const invoice = await this.invoiceService.getInvoiceById(parseInt(id), userId);
                if (!invoice) {
                    res.status(404).json({ message: 'Invoice not found' });
                    return;
                }
                res.status(200).json({ invoice });
            }
            catch (error) {
                console.error('Get invoice error:', error);
                res.status(500).json({ message: 'Internal server error' });
            }
        };
        this.updateInvoice = async (req, res) => {
            try {
                const { id } = req.params;
                const userId = req.user.id;
                const updateData = req.body;
                const invoice = await this.invoiceService.updateInvoice(parseInt(id), userId, updateData);
                if (!invoice) {
                    res.status(404).json({ message: 'Invoice not found' });
                    return;
                }
                res.status(200).json({
                    message: 'Invoice updated successfully',
                    invoice,
                });
            }
            catch (error) {
                console.error('Update invoice error:', error);
                res.status(500).json({ message: 'Internal server error' });
            }
        };
        this.deleteInvoice = async (req, res) => {
            try {
                const { id } = req.params;
                const userId = req.user.id;
                const deleted = await this.invoiceService.deleteInvoice(parseInt(id), userId);
                if (!deleted) {
                    res.status(404).json({ message: 'Invoice not found' });
                    return;
                }
                res.status(200).json({ message: 'Invoice deleted successfully' });
            }
            catch (error) {
                console.error('Delete invoice error:', error);
                res.status(500).json({ message: 'Internal server error' });
            }
        };
        this.generateInvoicePDF = async (req, res) => {
            try {
                const { id } = req.params;
                const userId = req.user.id;
                const pdfBuffer = await this.invoiceService.generateInvoicePDF(parseInt(id), userId);
                if (!pdfBuffer) {
                    res.status(404).json({ message: 'Invoice not found' });
                    return;
                }
                res.setHeader('Content-Type', 'application/pdf');
                res.setHeader('Content-Disposition', `attachment; filename=invoice-${id}.pdf`);
                res.send(pdfBuffer);
            }
            catch (error) {
                console.error('Generate PDF error:', error);
                res.status(500).json({ message: 'Internal server error' });
            }
        };
        this.invoiceService = new invoiceService_1.InvoiceService();
    }
}
exports.InvoiceController = InvoiceController;
//# sourceMappingURL=invoiceController.js.map