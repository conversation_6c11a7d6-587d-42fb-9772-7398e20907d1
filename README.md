# Invoice Generation Backend

A robust backend API for invoice generation built with Express.js, TypeScript, PostgreSQL, and Sequelize.

## Features

- **User Authentication & Authorization** - JWT-based auth with role-based access control
- **Invoice Management** - Complete CRUD operations for invoices
- **User Management** - Admin panel for user management
- **Database Integration** - PostgreSQL with Sequelize ORM
- **Type Safety** - Full TypeScript implementation
- **Validation** - Request validation middleware
- **Error Handling** - Centralized error handling
- **Security** - Password hashing, JWT tokens, CORS protection

## Project Structure

```
src/
├── server.ts              # Server startup and configuration
├── app.ts                 # Express app setup and base routes
├── config/
│   └── database.ts        # Database configuration
├── controllers/           # Request handlers
│   ├── authController.ts
│   ├── invoiceController.ts
│   └── userController.ts
├── models/               # Sequelize models
│   ├── User.ts
│   ├── Invoice.ts
│   └── index.ts
├── services/             # Business logic layer
│   ├── authService.ts
│   ├── invoiceService.ts
│   └── userService.ts
├── routes/               # API routes
│   ├── index.ts          # Main routes aggregator
│   ├── authRoutes.ts     # Authentication endpoints
│   ├── invoiceRoutes.ts  # Invoice management endpoints
│   └── userRoutes.ts     # User management endpoints
├── middleware/           # Custom middleware
│   ├── authMiddleware.ts
│   ├── validation.ts
│   └── errorHandler.ts
├── types/                # TypeScript type definitions
│   └── index.ts
└── utils/                # Utility functions
    └── logger.ts
```

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- PostgreSQL (v12 or higher)
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Create environment file:
   ```bash
   cp .env.example .env
   ```

4. Update the `.env` file with your database credentials and JWT secret

5. Create PostgreSQL database:
   ```sql
   CREATE DATABASE invoice_db;
   ```

6. Run the development server:
   ```bash
   npm run dev
   ```

## API Endpoints

### Base URL: `/api/v1`

### Authentication (`/api/v1/auth`)
- `POST /register` - Register new user
- `POST /login` - User login
- `GET /profile` - Get user profile (protected)
- `POST /logout` - Logout user (protected)

### Invoices (`/api/v1/invoices`)
- `POST /` - Create new invoice (protected)
- `GET /` - Get user invoices with pagination (protected)
- `GET /:id` - Get specific invoice (protected)
- `PUT /:id` - Update invoice (protected)
- `DELETE /:id` - Delete invoice (protected)
- `GET /:id/pdf` - Generate and download PDF (protected)
- `PATCH /:id/status` - Update invoice status (protected)

### Users (`/api/v1/users`) - Admin only
- `GET /` - Get all users with pagination
- `GET /:id` - Get specific user
- `PUT /:id` - Update user
- `DELETE /:id` - Delete user
- `PATCH /:id/toggle-status` - Toggle user status
- `GET /stats/overview` - Get user statistics

## Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm start` - Start production server
- `npm run clean` - Clean build directory
- `npm run seed` - Seed database with sample data
- `npm run db:sync` - Sync database models

## Testing the API

Once the server is running, you can test these endpoints:

- **Health check**: `GET http://localhost:3000/health`
- **API Info**: `GET http://localhost:3000/`
- **Register**: `POST http://localhost:3000/api/v1/auth/register`
- **Login**: `POST http://localhost:3000/api/v1/auth/login`
- **Get Profile**: `GET http://localhost:3000/api/v1/auth/profile` (requires token)
- **Create Invoice**: `POST http://localhost:3000/api/v1/invoices` (requires token)

## Environment Variables

```env
PORT=3000
NODE_ENV=development
DB_HOST=localhost
DB_PORT=5432
DB_NAME=invoice_db
DB_USER=postgres
DB_PASSWORD=password
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=24h
```

## Technologies Used

- **Express.js** - Web framework
- **TypeScript** - Type safety
- **PostgreSQL** - Database
- **Sequelize** - ORM
- **JWT** - Authentication
- **bcryptjs** - Password hashing
- **CORS** - Cross-origin resource sharing
- **dotenv** - Environment variables
