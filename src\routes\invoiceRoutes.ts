import { Router } from 'express';
import { InvoiceController } from '../controllers/invoiceController';
import { authMiddleware } from '../middleware/authMiddleware';
import { validateInvoice } from '../middleware/validation';

const router = Router();
const invoiceController = new InvoiceController();

// All routes are protected
router.use(authMiddleware);

/**
 * @route   POST /api/v1/invoices
 * @desc    Create a new invoice
 * @access  Private
 */
router.post('/', validateInvoice, invoiceController.createInvoice);

/**
 * @route   GET /api/v1/invoices
 * @desc    Get all invoices for the authenticated user
 * @access  Private
 * @query   page, limit, status
 */
router.get('/', invoiceController.getInvoices);

/**
 * @route   GET /api/v1/invoices/:id
 * @desc    Get a specific invoice by ID
 * @access  Private
 */
router.get('/:id', invoiceController.getInvoiceById);

/**
 * @route   PUT /api/v1/invoices/:id
 * @desc    Update an invoice
 * @access  Private
 */
router.put('/:id', validateInvoice, invoiceController.updateInvoice);

/**
 * @route   DELETE /api/v1/invoices/:id
 * @desc    Delete an invoice
 * @access  Private
 */
router.delete('/:id', invoiceController.deleteInvoice);

/**
 * @route   GET /api/v1/invoices/:id/pdf
 * @desc    Generate and download invoice PDF
 * @access  Private
 */
router.get('/:id/pdf', invoiceController.generateInvoicePDF);

/**
 * @route   PATCH /api/v1/invoices/:id/status
 * @desc    Update invoice status
 * @access  Private
 */
router.patch('/:id/status', (req, res) => {
  // This can be implemented later for status updates
  res.json({ message: 'Status update endpoint - to be implemented' });
});

export { router as invoiceRoutes };
