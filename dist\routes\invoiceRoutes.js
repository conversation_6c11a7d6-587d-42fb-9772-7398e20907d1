"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.invoiceRoutes = void 0;
const express_1 = require("express");
const invoiceController_1 = require("../controllers/invoiceController");
const authMiddleware_1 = require("../middleware/authMiddleware");
const validation_1 = require("../middleware/validation");
const router = (0, express_1.Router)();
exports.invoiceRoutes = router;
const invoiceController = new invoiceController_1.InvoiceController();
router.use(authMiddleware_1.authMiddleware);
router.post('/', validation_1.validateInvoice, invoiceController.createInvoice);
router.get('/', invoiceController.getInvoices);
router.get('/:id', invoiceController.getInvoiceById);
router.put('/:id', validation_1.validateInvoice, invoiceController.updateInvoice);
router.delete('/:id', invoiceController.deleteInvoice);
router.get('/:id/pdf', invoiceController.generateInvoicePDF);
router.patch('/:id/status', (req, res) => {
    res.json({ message: 'Status update endpoint - to be implemented' });
});
//# sourceMappingURL=invoiceRoutes.js.map