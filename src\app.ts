import 'dotenv/config';
import express from 'express';
import cors from 'cors';
import { errorHandler } from './middleware/errorHandler';
import { apiV1Routes } from './routes';
import { HealthController } from './controllers/healthController';

const app = express();

// Global middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Health check routes (outside API versioning)
app.get('/health', HealthController.healthCheck);
app.get('/status', HealthController.healthCheck);

// API v1 Routes
app.use('/api/v1', apiV1Routes);

// Root route
app.get('/', (_req, res) => {
  res.json({
    message: 'Invoice Generation Backend API',
    version: '1.0.0',
    endpoints: {
      health: '/health',
      auth: '/api/v1/auth',
      invoices: '/api/v1/invoices',
      users: '/api/v1/users'
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found',
    path: req.originalUrl
  });
});

// Error handling middleware (should be last)
app.use(errorHandler);

export default app;
