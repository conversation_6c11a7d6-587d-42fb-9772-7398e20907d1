"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv_1 = __importDefault(require("dotenv"));
const app_1 = __importDefault(require("./app"));
const database_1 = require("./config/database");
const logger_1 = require("./utils/logger");
dotenv_1.default.config();
const PORT = process.env.PORT || 3000;
const startServer = async () => {
    try {
        await database_1.sequelize.authenticate();
        logger_1.Logger.info('Database connected successfully');
        await database_1.sequelize.sync({ force: false });
        logger_1.Logger.info('Database synchronized');
        app_1.default.listen(PORT, () => {
            logger_1.Logger.info(`Server is running on port ${PORT}`);
            logger_1.Logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
            logger_1.Logger.info(`Health check available at: http://localhost:${PORT}/health`);
        });
    }
    catch (error) {
        logger_1.Logger.error('Unable to start server:', error);
        process.exit(1);
    }
};
process.on('unhandledRejection', (err) => {
    logger_1.Logger.error('Unhandled Promise Rejection:', err);
    process.exit(1);
});
process.on('uncaughtException', (err) => {
    logger_1.Logger.error('Uncaught Exception:', err);
    process.exit(1);
});
process.on('SIGTERM', async () => {
    logger_1.Logger.info('SIGTERM received. Shutting down gracefully...');
    await database_1.sequelize.close();
    process.exit(0);
});
process.on('SIGINT', async () => {
    logger_1.Logger.info('SIGINT received. Shutting down gracefully...');
    await database_1.sequelize.close();
    process.exit(0);
});
startServer();
//# sourceMappingURL=server.js.map