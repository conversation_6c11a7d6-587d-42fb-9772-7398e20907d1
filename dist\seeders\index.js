"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseSeeder = void 0;
const database_1 = require("../config/database");
const User_1 = require("../models/User");
const Invoice_1 = require("../models/Invoice");
const bcryptjs_1 = __importDefault(require("bcryptjs"));
class DatabaseSeeder {
    static async seed() {
        try {
            console.log('Starting database seeding...');
            const adminPassword = await bcryptjs_1.default.hash('admin123', 12);
            const admin = await User_1.User.findOrCreate({
                where: { email: '<EMAIL>' },
                defaults: {
                    name: 'Admin User',
                    email: '<EMAIL>',
                    password: adminPassword,
                    role: 'admin',
                    isActive: true,
                },
            });
            const userPassword = await bcryptjs_1.default.hash('user123', 12);
            const user = await User_1.User.findOrCreate({
                where: { email: '<EMAIL>' },
                defaults: {
                    name: 'Regular User',
                    email: '<EMAIL>',
                    password: userPassword,
                    role: 'user',
                    isActive: true,
                },
            });
            const sampleInvoice = await Invoice_1.Invoice.findOrCreate({
                where: { invoiceNumber: 'INV-2024-0001' },
                defaults: {
                    invoiceNumber: 'INV-2024-0001',
                    clientName: 'Sample Client',
                    clientEmail: '<EMAIL>',
                    clientAddress: '123 Main St, City, State 12345',
                    items: [
                        {
                            description: 'Web Development Services',
                            quantity: 40,
                            rate: 50,
                            amount: 2000,
                        },
                        {
                            description: 'Hosting Setup',
                            quantity: 1,
                            rate: 100,
                            amount: 100,
                        },
                    ],
                    subtotal: 2100,
                    tax: 210,
                    total: 2310,
                    status: 'sent',
                    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
                    userId: user[0].id,
                },
            });
            console.log('Database seeding completed successfully!');
            console.log('Admin credentials: <EMAIL> / admin123');
            console.log('User credentials: <EMAIL> / user123');
        }
        catch (error) {
            console.error('Error seeding database:', error);
            throw error;
        }
    }
    static async run() {
        try {
            await database_1.sequelize.authenticate();
            console.log('Database connected successfully');
            await database_1.sequelize.sync({ force: false });
            console.log('Database synchronized');
            await this.seed();
            process.exit(0);
        }
        catch (error) {
            console.error('Seeding failed:', error);
            process.exit(1);
        }
    }
}
exports.DatabaseSeeder = DatabaseSeeder;
if (require.main === module) {
    DatabaseSeeder.run();
}
//# sourceMappingURL=index.js.map