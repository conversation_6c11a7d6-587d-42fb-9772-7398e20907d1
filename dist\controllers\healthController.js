"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealthController = void 0;
const database_1 = require("../config/database");
class HealthController {
    static async healthCheck(req, res) {
        try {
            await database_1.sequelize.authenticate();
            const healthStatus = {
                status: 'OK',
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                environment: process.env.NODE_ENV || 'development',
                version: process.env.npm_package_version || '1.0.0',
                database: 'Connected',
                memory: {
                    used: Math.round((process.memoryUsage().heapUsed / 1024 / 1024) * 100) / 100,
                    total: Math.round((process.memoryUsage().heapTotal / 1024 / 1024) * 100) / 100,
                },
            };
            res.status(200).json(healthStatus);
        }
        catch (error) {
            res.status(503).json({
                status: 'ERROR',
                timestamp: new Date().toISOString(),
                error: 'Database connection failed',
            });
        }
    }
}
exports.HealthController = HealthController;
//# sourceMappingURL=healthController.js.map